import SwiftUI
import PencilKit

// MARK: - Enhanced Tamil Writing View with Lesson Integration

struct WritingView: View {
    @StateObject private var scriptService = TamilScriptService()
    @StateObject private var integrationService = LessonWritingIntegrationService.shared
    @StateObject private var assessmentEngine = TamilWritingAssessmentEngine.shared

    @State private var selectedMode: WritingMode = .guided
    @State private var selectedCharacter: TamilCharacter?
    @State private var selectedLesson: String?
    @State private var showingLessonPicker = false
    @State private var writingExercises: [LessonWritingIntegrationService.VocabularyWritingExercise] = []
    @State private var currentExerciseIndex = 0
    @State private var isLoading = true
    @State private var showingWritingCanvas = false

    let language: Language

    // Computed progress for curriculum modules
    private var vowelProgress: Double {
        let vowels = scriptService.allCharacters.filter { $0.characterType == .vowel }
        return vowels.isEmpty ? 0.0 : Double(vowels.count) / 12.0 // Progress based on loaded vowels
    }

    var body: some View {
        VStack(spacing: 0) {
            // Enhanced Writing Header
            EnhancedWritingHeader(
                language: language,
                selectedMode: $selectedMode,
                selectedLesson: selectedLesson
            )

            // Main Content
            if isLoading {
                loadingView
            } else {
                enhancedWritingContentView
            }
        }
        .background(
            LinearGradient(
                colors: [Color.green.opacity(0.05), Color.blue.opacity(0.05)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .navigationBarHidden(true)
        .task {
            await loadWritingContent()
        }
        .sheet(isPresented: $showingLessonPicker) {
            LessonPickerView(
                selectedLesson: $selectedLesson,
                onLessonSelected: loadLessonExercises
            )
        }
        .fullScreenCover(isPresented: $showingWritingCanvas) {
            if let character = selectedCharacter {
                TamilWritingCanvasContainer(
                    character: character,
                    writingMode: selectedMode,
                    onComplete: handleWritingComplete
                )
            }
        }
    }
    
    // MARK: - Views

    private var loadingView: some View {
        VStack(spacing: 20) {
            ProgressView()
                .scaleEffect(1.5)

            Text("Loading Tamil writing system...")
                .font(.headline)
                .foregroundColor(.secondary)

            Text("Preparing characters, stroke orders, and lesson integration")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var enhancedWritingContentView: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Tamil Script Learning - Primary Content
                characterSelectionCard

                // Writing Mode Info (only show if lesson is selected)
                if selectedLesson != nil {
                    WritingModeInfoCard(mode: selectedMode, language: language)
                }

                // Lesson Integration Section (only show if lesson is selected)
                if selectedLesson != nil {
                    lessonIntegrationCard
                }

                // Exercise Display (only show if lesson is selected and exercises available)
                if selectedLesson != nil && !writingExercises.isEmpty {
                    vocabularyExerciseCard
                }

                // Quick Practice Options (moved to bottom and simplified)
                if selectedLesson == nil {
                    quickPracticeSection
                }
            }
            .padding()
        }
    }

    private var lessonIntegrationCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "book.fill")
                    .font(.title2)
                    .foregroundColor(.blue)

                Text("Lesson Integration")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Button("Select Lesson") {
                    showingLessonPicker = true
                }
                .buttonStyle(.bordered)
            }

            if let lesson = selectedLesson {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text(lesson.replacingOccurrences(of: "_", with: " "))
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Spacer()

                        if !writingExercises.isEmpty {
                            Text("\(writingExercises.count) exercises")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    if !writingExercises.isEmpty {
                        ProgressView(value: Double(currentExerciseIndex), total: Double(writingExercises.count))
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))

                        Text("Exercise \(currentExerciseIndex + 1) of \(writingExercises.count)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue.opacity(0.1))
                )
            } else {
                Text("Select a lesson to practice vocabulary writing with guided exercises")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .italic()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    private var vocabularyExerciseCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Vocabulary Writing Exercise")
                .font(.headline)
                .fontWeight(.semibold)

            if !writingExercises.isEmpty {
                let currentExercise = writingExercises[currentExerciseIndex]

                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text(currentExercise.vocabularyWord.tamil)
                            .font(.title)
                            .fontWeight(.bold)

                        Text("(\(currentExercise.vocabularyWord.romanization))")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Spacer()

                        Text(currentExercise.difficulty.displayName)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.orange.opacity(0.2))
                            .foregroundColor(.orange)
                            .cornerRadius(8)
                    }

                    Text(currentExercise.vocabularyWord.english)
                        .font(.body)
                        .foregroundColor(.secondary)

                    if let context = currentExercise.culturalContext {
                        Text(context)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .italic()
                            .padding(.top, 4)
                    }

                    HStack {
                        Button("Start Writing") {
                            selectedCharacter = currentExercise.targetCharacters.first
                            showingWritingCanvas = true
                        }
                        .buttonStyle(.borderedProminent)

                        Spacer()

                        HStack(spacing: 8) {
                            Button("Previous") {
                                if currentExerciseIndex > 0 {
                                    currentExerciseIndex -= 1
                                    updateSelectedCharacter()
                                }
                            }
                            .disabled(currentExerciseIndex == 0)
                            .buttonStyle(.bordered)

                            Button("Next") {
                                if currentExerciseIndex < writingExercises.count - 1 {
                                    currentExerciseIndex += 1
                                    updateSelectedCharacter()
                                }
                            }
                            .disabled(currentExerciseIndex >= writingExercises.count - 1)
                            .buttonStyle(.bordered)
                        }
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.secondarySystemBackground))
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }

    private var characterSelectionCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Tamil Script Learning")
                .font(.headline)
                .fontWeight(.semibold)

            Text("Follow the structured curriculum to master Tamil writing")
                .font(.subheadline)
                .foregroundColor(.secondary)

            // Curriculum Modules
            VStack(spacing: 12) {
                // Vowels Module
                NavigationLink(destination: TamilVowelsLearningView(scriptService: scriptService)) {
                    CurriculumModuleCard(
                        title: "உயிர் எழுத்துகள் (Vowels)",
                        subtitle: "Master 12 Tamil vowels",
                        icon: "textformat.abc",
                        progress: vowelProgress,
                        isRecommended: true
                    )
                }
                .buttonStyle(PlainButtonStyle())

                // Consonants Module (Coming Soon)
                CurriculumModuleCard(
                    title: "மெய் எழுத்துகள் (Consonants)",
                    subtitle: "Learn 18 Tamil consonants",
                    icon: "textformat.123",
                    progress: 0.0,
                    isComingSoon: true
                )

                // Combinations Module (Coming Soon)
                CurriculumModuleCard(
                    title: "உயிர்மெய் (Combinations)",
                    subtitle: "Practice character combinations",
                    icon: "textformat.size",
                    progress: 0.0,
                    isComingSoon: true
                )
            }

            // Quick Character Practice
            if !scriptService.allCharacters.isEmpty {
                Divider()

                Text("Quick Practice")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .padding(.top, 8)

                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 12) {
                    ForEach(scriptService.allCharacters.prefix(8), id: \.id) { character in
                        CharacterSelectionButton(
                            character: character,
                            isSelected: selectedCharacter?.id == character.id,
                            action: {
                                selectedCharacter = character
                                showingWritingCanvas = true
                            }
                        )
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }

    private var quickPracticeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Additional Practice")
                .font(.headline)
                .fontWeight(.semibold)
                .padding(.horizontal)

            VStack(spacing: 8) {
                // Quick Practice Button (simplified)
                Button(action: {
                    if let randomChar = scriptService.allCharacters.randomElement() {
                        selectedCharacter = randomChar
                        selectedMode = .freeform
                        showingWritingCanvas = true
                    }
                }) {
                    HStack {
                        Image(systemName: "pencil.and.outline")
                            .foregroundColor(.orange)
                        Text("Quick Practice")
                            .fontWeight(.medium)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
                .buttonStyle(PlainButtonStyle())

                // Assessment Button (simplified)
                Button(action: {
                    selectedMode = .assessment
                    if let character = selectedCharacter ?? scriptService.allCharacters.first {
                        selectedCharacter = character
                        showingWritingCanvas = true
                    }
                }) {
                    HStack {
                        Image(systemName: "checkmark.circle")
                            .foregroundColor(.green)
                        Text("Assessment Mode")
                            .fontWeight(.medium)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(.horizontal)
        }
    }

    // MARK: - Data Loading

    private func loadWritingContent() async {
        isLoading = true
        defer { isLoading = false }

        print("🔄 Loading Tamil writing content...")

        // For now, use mock data to get UI working
        // TODO: Implement proper database loading later
        await loadMockTamilCharacters()

        print("✅ Loaded \(scriptService.allCharacters.count) Tamil characters")
        print("📝 Vowels: \(scriptService.allCharacters.filter { $0.characterType == .vowel }.count)")
        print("📝 Consonants: \(scriptService.allCharacters.filter { $0.characterType == .consonant }.count)")
        print("✅ Tamil Script Learning ready with \(scriptService.allCharacters.count) characters")
    }

    private func loadMockTamilCharacters() async {
        // Create mock Tamil characters for UI testing
        let mockVowels = [
            TamilCharacter(
                character: "அ",
                characterType: .vowel,
                unicodeValue: "U+0B85",
                romanization: "a",
                characterNameEnglish: "Tamil Letter A",
                characterNameTamil: "அ",
                difficultyLevel: 1,
                strokeCount: 2,
                writingComplexity: .simple,
                learningOrder: 1
            ),
            TamilCharacter(
                character: "ஆ",
                characterType: .vowel,
                unicodeValue: "U+0B86",
                romanization: "aa",
                characterNameEnglish: "Tamil Letter Aa",
                characterNameTamil: "ஆ",
                difficultyLevel: 1,
                strokeCount: 3,
                writingComplexity: .simple,
                learningOrder: 2
            ),
            TamilCharacter(
                character: "இ",
                characterType: .vowel,
                unicodeValue: "U+0B87",
                romanization: "i",
                characterNameEnglish: "Tamil Letter I",
                characterNameTamil: "இ",
                difficultyLevel: 1,
                strokeCount: 2,
                writingComplexity: .simple,
                learningOrder: 3
            )
        ]

        let mockConsonants = [
            TamilCharacter(
                character: "க",
                characterType: .consonant,
                unicodeValue: "U+0B95",
                romanization: "ka",
                characterNameEnglish: "Tamil Letter Ka",
                characterNameTamil: "க",
                difficultyLevel: 2,
                strokeCount: 3,
                writingComplexity: .moderate,
                learningOrder: 13
            ),
            TamilCharacter(
                character: "ங",
                characterType: .consonant,
                unicodeValue: "U+0B99",
                romanization: "nga",
                characterNameEnglish: "Tamil Letter Nga",
                characterNameTamil: "ங",
                difficultyLevel: 2,
                strokeCount: 4,
                writingComplexity: .moderate,
                learningOrder: 14
            )
        ]

        // Set mock data in the service
        await MainActor.run {
            scriptService.allCharacters = mockVowels + mockConsonants
        }
    }
    
    private func loadLessonExercises(lessonId: String) {
        Task {
            await integrationService.generateWritingExercisesForLesson(lessonId)
            writingExercises = integrationService.getWritingExercisesForLesson(lessonId)
            currentExerciseIndex = 0
            updateSelectedCharacter()
        }
    }

    private func updateSelectedCharacter() {
        if !writingExercises.isEmpty && currentExerciseIndex < writingExercises.count {
            let exercise = writingExercises[currentExerciseIndex]
            selectedCharacter = exercise.targetCharacters.first
        }
    }

    private func handleWritingComplete(_ result: WritingResult) {
        // Handle completion of writing practice
        print("Writing completed with accuracy: \(result.accuracy)")

        // Save progress to assessment engine
        Task {
            if let userId = UUID(uuidString: "user-id") { // This would come from auth
                let assessment = await assessmentEngine.assessWriting(
                    character: result.character,
                    userStrokes: result.strokes,
                    writingMode: result.writingMode,
                    userId: userId,
                    startTime: Date().addingTimeInterval(-result.completionTime)
                )

                print("Assessment completed: \(assessment.grade.displayName)")
            }
        }

        // Auto-advance to next exercise if in lesson mode
        if selectedLesson != nil && currentExerciseIndex < writingExercises.count - 1 {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                currentExerciseIndex += 1
                updateSelectedCharacter()
            }
        }
    }
}

// MARK: - Enhanced Writing Header

struct EnhancedWritingHeader: View {
    let language: Language
    @Binding var selectedMode: WritingMode
    let selectedLesson: String?

    var body: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Tamil Writing Practice")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    if let lesson = selectedLesson {
                        Text("Lesson: \(lesson.replacingOccurrences(of: "_", with: " "))")
                            .font(.subheadline)
                            .foregroundColor(.blue)
                            .fontWeight(.medium)
                    } else {
                        Text("Master Tamil script with AI-powered guidance")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                VStack(spacing: 4) {
                    Text(language.flag)
                        .font(.system(size: 32))

                    Text("தமிழ்")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                }
            }

            // Enhanced Mode Selector
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(WritingMode.allCases, id: \.self) { mode in
                        EnhancedWritingModeChip(
                            mode: mode,
                            isSelected: selectedMode == mode,
                            action: {
                                withAnimation(.spring()) {
                                    selectedMode = mode
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

// MARK: - Enhanced Writing Mode Chip

struct EnhancedWritingModeChip: View {
    let mode: WritingMode
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: mode.icon)
                    .font(.title3)

                Text(mode.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? mode.color : Color(.secondarySystemBackground))
            )
            .foregroundColor(isSelected ? .white : .primary)
            .shadow(color: isSelected ? mode.color.opacity(0.3) : .clear, radius: 8, x: 0, y: 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Character Selection Button

struct CharacterSelectionButton: View {
    let character: TamilCharacter
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(character.character)
                    .font(.title)
                    .fontWeight(.medium)

                Text(character.romanization)
                    .font(.caption)
                    .foregroundColor(.secondary)

                // Difficulty indicator
                HStack(spacing: 2) {
                    ForEach(1...character.difficultyLevel, id: \.self) { _ in
                        Circle()
                            .fill(character.writingComplexity.color)
                            .frame(width: 4, height: 4)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue.opacity(0.2) : Color(.secondarySystemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Assessment Mode Button

struct AssessmentModeButton: View {
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: "checkmark.circle.fill")
                    .font(.title2)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Assessment Mode")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text("Test your Tamil writing skills")
                        .font(.subheadline)
                        .opacity(0.8)
                }

                Spacer()

                Image(systemName: "arrow.right.circle.fill")
                    .font(.title2)
            }
            .foregroundColor(.white)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [.orange, .red],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Lesson Picker View

struct LessonPickerView: View {
    @Binding var selectedLesson: String?
    let onLessonSelected: (String) -> Void
    @Environment(\.dismiss) private var dismiss

    private let availableLessons = [
        "A1_BASIC_GREETINGS",
        "A1_FAMILY_MEMBERS",
        "A1_NUMBERS",
        "A1_COLORS",
        "A1_FOOD"
    ]

    var body: some View {
        NavigationView {
            List(availableLessons, id: \.self) { lesson in
                Button(action: {
                    selectedLesson = lesson
                    onLessonSelected(lesson)
                    dismiss()
                }) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(lesson.replacingOccurrences(of: "_", with: " "))
                                .font(.headline)
                                .foregroundColor(.primary)

                            Text("A1 Level • Beginner")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        if selectedLesson == lesson {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.blue)
                        }
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
            .navigationTitle("Select Lesson")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Writing Mode Chip

struct WritingModeChip: View {
    let mode: WritingMode
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: mode.icon)
                    .font(.caption)
                
                Text(mode.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? Color.green : Color(.secondarySystemBackground))
            )
            .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Writing Mode Info Card

struct WritingModeInfoCard: View {
    let mode: WritingMode
    let language: Language
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: mode.icon)
                    .font(.title2)
                    .foregroundColor(.green)
                
                Text(mode.displayName)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            Text(mode.description)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Writing Lesson Card

struct WritingLessonCard: View {
    let lesson: WritingLesson
    let language: Language
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(lesson.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                        
                        Text(lesson.subtitle)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    VStack(spacing: 4) {
                        Text("\(lesson.estimatedTime) min")
                            .font(.caption)
                            .foregroundColor(.green)
                        
                        Text("⭐ \(lesson.completionReward)")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                
                // Character Preview
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(lesson.characters, id: \.self) { character in
                            Text(character)
                                .font(.title)
                                .foregroundColor(.primary)
                                .padding(8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color(.secondarySystemBackground))
                                )
                        }
                    }
                    .padding(.horizontal)
                }
                
                // Instructions
                Text(lesson.instructions)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .italic()
                
                // Difficulty Indicator
                HStack {
                    ForEach(1...5, id: \.self) { level in
                        Image(systemName: level <= lesson.difficulty ? "star.fill" : "star")
                            .font(.caption)
                            .foregroundColor(level <= lesson.difficulty ? .orange : .gray)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.green)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Quick Practice Button

struct QuickPracticeButton: View {
    let language: Language
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: "pencil.and.outline")
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Quick Practice")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("5-minute \(language.displayName) writing session")
                        .font(.subheadline)
                        .opacity(0.8)
                }
                
                Spacer()
                
                Image(systemName: "arrow.right.circle.fill")
                    .font(.title2)
            }
            .foregroundColor(.white)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [.green, .blue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Supporting Types
// WritingMode is defined in TamilWritingModels.swift

struct WritingLesson: Identifiable {
    let id: String
    let title: String
    let subtitle: String
    let mode: WritingMode
    let characters: [String]
    let instructions: String
    let difficulty: Int // 1-5 stars
    let estimatedTime: Int // minutes
    let completionReward: Int // points
}

// MARK: - Writing Pad View (Placeholder for full implementation)

struct WritingPadView: View {
    let lesson: WritingLesson
    let language: Language
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Writing Practice: \(lesson.title)")
                    .font(.headline)
                    .padding()
                
                // TODO: Implement PencilKit canvas and character recognition
                Rectangle()
                    .fill(Color(.systemGray6))
                    .overlay(
                        Text("Writing Canvas\n(PencilKit Integration)")
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                    )
                    .padding()
                
                Spacer()
            }
            .navigationTitle("Writing Practice")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Curriculum Module Card

struct CurriculumModuleCard: View {
    let title: String
    let subtitle: String
    let icon: String
    let progress: Double
    var isRecommended: Bool = false
    var isComingSoon: Bool = false

    var body: some View {
        HStack(spacing: 16) {
            // Icon
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(isComingSoon ? .secondary : .blue)
                .frame(width: 32, height: 32)

            // Content
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(isComingSoon ? .secondary : .primary)

                    if isRecommended {
                        Text("RECOMMENDED")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.green)
                            .cornerRadius(4)
                    }

                    if isComingSoon {
                        Text("COMING SOON")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.orange)
                            .cornerRadius(4)
                    }

                    Spacer()
                }

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)

                if !isComingSoon && progress > 0 {
                    ProgressView(value: progress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                        .scaleEffect(y: 0.8)

                    Text("\(Int(progress * 100))% complete")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }

            // Arrow
            if !isComingSoon {
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
        .opacity(isComingSoon ? 0.6 : 1.0)
    }
}

#Preview {
    WritingView(language: .tamil)
}