//
//  AdaptiveLearningEngine.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 02/07/2025.
//

import Foundation
import Combine
import CoreML
import SwiftUI

@MainActor
class AdaptiveLearningEngine: ObservableObject {
    static let shared = AdaptiveLearningEngine()
    
    @Published var currentLearningPath: LearningPath?
    @Published var adaptiveRecommendations: [AdaptiveRecommendation] = []
    @Published var difficultyAdjustments: [DifficultyAdjustment] = []
    @Published var personalizedContent: [PersonalizedContent] = []
    @Published var learningState: LearningState = .analyzing
    
    private let analyticsService = TamilWritingAnalyticsService.shared
    private let aiService = TamilWritingAIFeedbackService.shared
    private let scriptService = TamilScriptService.shared
    
    private var learningModel: MLModel?
    private var userProfile: UserLearningProfile?
    
    enum LearningState {
        case analyzing
        case adapting
        case ready
        case error(String)
        
        var displayText: String {
            switch self {
            case .analyzing: return "Analyzing your learning patterns..."
            case .adapting: return "Adapting content to your needs..."
            case .ready: return "Personalized learning ready"
            case .error(let message): return "Error: \(message)"
            }
        }
    }
    
    // MARK: - Core Data Models
    
    struct LearningPath: Identifiable, Codable {
        let id = UUID()
        let userId: String
        let pathType: PathType
        let currentLevel: CEFRLevel
        let targetLevel: CEFRLevel
        let estimatedDuration: Int // days
        let milestones: [Milestone]
        let adaptiveSteps: [AdaptiveStep]
        let personalizedGoals: [PersonalizedGoal]
        let createdAt: Date
        let lastUpdated: Date
        
        enum PathType: String, Codable, CaseIterable {
            case accelerated = "accelerated"
            case standard = "standard"
            case reinforcement = "reinforcement"
            case remedial = "remedial"
            
            var displayName: String {
                switch self {
                case .accelerated: return "Accelerated Learning"
                case .standard: return "Standard Progression"
                case .reinforcement: return "Skill Reinforcement"
                case .remedial: return "Foundation Building"
                }
            }
            
            var description: String {
                switch self {
                case .accelerated: return "Fast-paced learning for quick progress"
                case .standard: return "Balanced approach with steady progression"
                case .reinforcement: return "Focus on strengthening existing skills"
                case .remedial: return "Extra support for challenging areas"
                }
            }
        }
        
        struct Milestone: Identifiable, Codable {
            let id = UUID()
            let title: String
            let description: String
            let targetDate: Date
            let requirements: [String]
            let rewards: [String]
            let isCompleted: Bool
            let completedDate: Date?
        }
        
        struct AdaptiveStep: Identifiable, Codable {
            let id = UUID()
            let stepNumber: Int
            let title: String
            let content: StepContent
            let difficulty: DifficultyLevel
            let estimatedTime: Int // minutes
            let prerequisites: [String]
            let adaptiveElements: [AdaptiveElement]
            let isUnlocked: Bool
            let isCompleted: Bool
            
            enum DifficultyLevel: String, Codable, CaseIterable {
                case beginner = "beginner"
                case elementary = "elementary"
                case intermediate = "intermediate"
                case advanced = "advanced"
                case expert = "expert"
                
                var numericValue: Double {
                    switch self {
                    case .beginner: return 1.0
                    case .elementary: return 2.0
                    case .intermediate: return 3.0
                    case .advanced: return 4.0
                    case .expert: return 5.0
                    }
                }
                
                var color: Color {
                    switch self {
                    case .beginner: return .green
                    case .elementary: return .blue
                    case .intermediate: return .orange
                    case .advanced: return .red
                    case .expert: return .purple
                    }
                }
            }
            
            struct StepContent: Codable {
                let contentType: ContentType
                let characters: [String]
                let exercises: [Exercise]
                let assessments: [Assessment]
                let resources: [Resource]
                
                enum ContentType: String, Codable {
                    case characterIntroduction = "character_introduction"
                    case strokePractice = "stroke_practice"
                    case wordFormation = "word_formation"
                    case sentenceWriting = "sentence_writing"
                    case conversationPractice = "conversation_practice"
                    case assessment = "assessment"
                }
                
                struct Exercise: Identifiable, Codable {
                    let id = UUID()
                    let type: ExerciseType
                    let instruction: String
                    let targetCharacters: [String]
                    let adaptiveParameters: AdaptiveParameters
                    
                    enum ExerciseType: String, Codable {
                        case guided = "guided"
                        case freeform = "freeform"
                        case recognition = "recognition"
                        case speed = "speed"
                        case accuracy = "accuracy"
                    }
                }
                
                struct Assessment: Identifiable, Codable {
                    let id = UUID()
                    let type: AssessmentType
                    let criteria: [String]
                    let passingScore: Double
                    let adaptiveScoring: Bool
                    
                    enum AssessmentType: String, Codable {
                        case formative = "formative"
                        case summative = "summative"
                        case diagnostic = "diagnostic"
                        case adaptive = "adaptive"
                    }
                }
                
                struct Resource: Identifiable, Codable {
                    let id = UUID()
                    let type: ResourceType
                    let title: String
                    let content: String
                    let mediaUrl: String?
                    
                    enum ResourceType: String, Codable {
                        case video = "video"
                        case audio = "audio"
                        case text = "text"
                        case interactive = "interactive"
                    }
                }
            }
            
            struct AdaptiveElement: Identifiable, Codable {
                let id = UUID()
                let elementType: ElementType
                let condition: AdaptiveCondition
                let action: AdaptiveAction
                
                enum ElementType: String, Codable {
                    case difficultyAdjustment = "difficulty_adjustment"
                    case contentModification = "content_modification"
                    case paceAdjustment = "pace_adjustment"
                    case supportLevel = "support_level"
                }
                
                struct AdaptiveCondition: Codable {
                    let metric: String
                    let comparisonOperator: ComparisonOperator
                    let threshold: Double
                    let timeWindow: Int // minutes
                    
                    enum ComparisonOperator: String, Codable {
                        case greaterThan = ">"
                        case lessThan = "<"
                        case equalTo = "=="
                        case greaterThanOrEqual = ">="
                        case lessThanOrEqual = "<="
                    }
                }
                
                struct AdaptiveAction: Codable {
                    let actionType: ActionType
                    let parameters: [String: Double]
                    let message: String?
                    
                    enum ActionType: String, Codable {
                        case increaseDifficulty = "increase_difficulty"
                        case decreaseDifficulty = "decrease_difficulty"
                        case addSupport = "add_support"
                        case removeSupport = "remove_support"
                        case changeContent = "change_content"
                        case adjustPace = "adjust_pace"
                    }
                }
            }
        }
        
        struct PersonalizedGoal: Identifiable, Codable {
            let id = UUID()
            let goalType: GoalType
            let title: String
            let description: String
            let targetValue: Double
            let currentValue: Double
            let deadline: Date
            let priority: Priority
            
            enum GoalType: String, Codable {
                case accuracy = "accuracy"
                case speed = "speed"
                case consistency = "consistency"
                case retention = "retention"
                case mastery = "mastery"
            }
            
            enum Priority: String, Codable {
                case low = "low"
                case medium = "medium"
                case high = "high"
                case critical = "critical"
            }
        }
    }
    
    struct AdaptiveRecommendation: Identifiable, Codable {
        let id = UUID()
        let recommendationType: RecommendationType
        let title: String
        let description: String
        let reasoning: String
        let confidence: Double
        let impact: Impact
        let actionItems: [ActionItem]
        let validUntil: Date
        let priority: Priority
        
        enum RecommendationType: String, Codable {
            case contentAdjustment = "content_adjustment"
            case difficultyChange = "difficulty_change"
            case paceModification = "pace_modification"
            case focusArea = "focus_area"
            case learningStrategy = "learning_strategy"
            case practiceSchedule = "practice_schedule"
        }
        
        enum Impact: String, Codable {
            case low = "low"
            case medium = "medium"
            case high = "high"
            case critical = "critical"
            
            var color: Color {
                switch self {
                case .low: return .gray
                case .medium: return .blue
                case .high: return .orange
                case .critical: return .red
                }
            }
        }
        
        enum Priority: String, Codable {
            case low = "low"
            case medium = "medium"
            case high = "high"
            case urgent = "urgent"
        }
        
        struct ActionItem: Identifiable, Codable {
            let id = UUID()
            let action: String
            let estimatedTime: Int
            let difficulty: String
            let expectedOutcome: String
        }
    }
    
    struct DifficultyAdjustment: Identifiable, Codable {
        let id = UUID()
        let characterId: String
        let currentDifficulty: Double
        let recommendedDifficulty: Double
        let adjustmentReason: AdjustmentReason
        let confidence: Double
        let appliedAt: Date?
        
        enum AdjustmentReason: String, Codable {
            case tooEasy = "too_easy"
            case tooHard = "too_hard"
            case perfectMatch = "perfect_match"
            case needsReinforcement = "needs_reinforcement"
            case readyToAdvance = "ready_to_advance"
        }
    }
    
    struct PersonalizedContent: Identifiable, Codable {
        let id = UUID()
        let contentType: ContentType
        let title: String
        let description: String
        let targetCharacters: [String]
        let adaptiveParameters: AdaptiveParameters
        let generatedAt: Date
        let validUntil: Date
        
        enum ContentType: String, Codable {
            case customExercise = "custom_exercise"
            case reinforcementPractice = "reinforcement_practice"
            case challengeMode = "challenge_mode"
            case reviewSession = "review_session"
            case speedTraining = "speed_training"
            case accuracyFocus = "accuracy_focus"
        }
    }
    
    struct AdaptiveParameters: Codable {
        let difficultyMultiplier: Double
        let supportLevel: Double
        let paceAdjustment: Double
        let repetitionCount: Int
        let feedbackFrequency: Double
        let hintAvailability: Bool
        let timeLimit: Int?
        let adaptiveScoring: Bool
    }
    
    struct UserLearningProfile: Codable {
        let userId: String
        let learningStyle: LearningStyle
        let cognitiveLoad: CognitiveLoad
        let motivationFactors: [MotivationFactor]
        let performancePatterns: PerformancePatterns
        let preferences: LearningPreferences
        let strengths: [String]
        let challenges: [String]
        let lastUpdated: Date
        
        enum LearningStyle: String, Codable {
            case visual = "visual"
            case auditory = "auditory"
            case kinesthetic = "kinesthetic"
            case readingWriting = "reading_writing"
            case multimodal = "multimodal"
        }
        
        enum CognitiveLoad: String, Codable {
            case low = "low"
            case medium = "medium"
            case high = "high"
            case variable = "variable"
        }
        
        enum MotivationFactor: String, Codable {
            case achievement = "achievement"
            case progress = "progress"
            case competition = "competition"
            case mastery = "mastery"
            case social = "social"
            case intrinsic = "intrinsic"
        }
        
        struct PerformancePatterns: Codable {
            let optimalSessionLength: Int
            let bestPerformanceTime: String
            let learningVelocity: Double
            let retentionRate: Double
            let errorPatterns: [String]
            let improvementAreas: [String]
        }
        
        struct LearningPreferences: Codable {
            let preferredDifficulty: Double
            let feedbackFrequency: String
            let supportLevel: String
            let practiceMode: String
            let goalOrientation: String
        }
    }
    
    private init() {
        loadAdaptiveLearningModel()
        setupLearningEngine()
    }
    
    // MARK: - Adaptive Learning Engine
    
    /// Generate personalized learning path based on user's performance and preferences
    func generateAdaptiveLearningPath(for userId: String) async -> LearningPath {
        learningState = .analyzing
        
        // Analyze user's current performance
        let userAnalysis = await analyzeUserPerformance(userId: userId)
        
        // Generate user learning profile
        let profile = await generateUserLearningProfile(userId: userId, analysis: userAnalysis)
        userProfile = profile
        
        learningState = .adapting
        
        // Determine optimal learning path type
        let pathType = determineOptimalPathType(profile: profile, analysis: userAnalysis)
        
        // Generate adaptive steps
        let adaptiveSteps = await generateAdaptiveSteps(
            pathType: pathType,
            profile: profile,
            analysis: userAnalysis
        )
        
        // Create personalized goals
        let personalizedGoals = generatePersonalizedGoals(profile: profile, analysis: userAnalysis)
        
        // Generate milestones
        let milestones = generateLearningMilestones(
            pathType: pathType,
            steps: adaptiveSteps,
            goals: personalizedGoals
        )
        
        let learningPath = LearningPath(
            userId: userId,
            pathType: pathType,
            currentLevel: userAnalysis.currentLevel,
            targetLevel: userAnalysis.targetLevel,
            estimatedDuration: calculateEstimatedDuration(pathType: pathType, steps: adaptiveSteps),
            milestones: milestones,
            adaptiveSteps: adaptiveSteps,
            personalizedGoals: personalizedGoals,
            createdAt: Date(),
            lastUpdated: Date()
        )
        
        currentLearningPath = learningPath
        learningState = .ready
        
        return learningPath
    }
    
    /// Adapt learning content based on real-time performance
    func adaptLearningContent(
        userId: String,
        currentPerformance: LearningPerformanceMetrics,
        sessionData: SessionData
    ) async -> [AdaptiveRecommendation] {
        
        guard let profile = userProfile else {
            return []
        }
        
        var recommendations: [AdaptiveRecommendation] = []
        
        // Analyze current performance against expected performance
        let performanceGap = analyzePerformanceGap(
            current: currentPerformance,
            expected: profile.performancePatterns,
            session: sessionData
        )
        
        // Generate difficulty adjustments
        let difficultyAdjustments = generateDifficultyAdjustments(
            performanceGap: performanceGap,
            profile: profile
        )
        
        // Generate content recommendations
        let contentRecommendations = generateContentRecommendations(
            performanceGap: performanceGap,
            profile: profile,
            session: sessionData
        )
        
        // Generate pacing recommendations
        let pacingRecommendations = generatePacingRecommendations(
            performanceGap: performanceGap,
            profile: profile
        )
        
        recommendations.append(contentsOf: difficultyAdjustments)
        recommendations.append(contentsOf: contentRecommendations)
        recommendations.append(contentsOf: pacingRecommendations)
        
        adaptiveRecommendations = recommendations
        
        return recommendations
    }
    
    /// Apply adaptive adjustments to current learning session
    func applyAdaptiveAdjustments(
        recommendations: [AdaptiveRecommendation]
    ) async -> PersonalizedContent {
        
        // Process recommendations and generate personalized content
        let adaptiveParameters = calculateAdaptiveParameters(from: recommendations)
        
        let personalizedContent = PersonalizedContent(
            contentType: determineContentType(from: recommendations),
            title: generateContentTitle(from: recommendations),
            description: generateContentDescription(from: recommendations),
            targetCharacters: extractTargetCharacters(from: recommendations),
            adaptiveParameters: adaptiveParameters,
            generatedAt: Date(),
            validUntil: Calendar.current.date(byAdding: .hour, value: 2, to: Date()) ?? Date()
        )
        
        return personalizedContent
    }
    
    // MARK: - Performance Analysis
    
    private func analyzeUserPerformance(userId: String) async -> UserPerformanceAnalysis {
        // Analyze user's writing performance data
        // This would integrate with the analytics service
        
        return UserPerformanceAnalysis(
            userId: userId,
            currentLevel: .a1,
            targetLevel: .a2,
            averageAccuracy: 0.75,
            learningVelocity: 2.1,
            consistencyScore: 0.68,
            retentionRate: 0.82,
            strongAreas: ["vowel_formation", "stroke_order"],
            weakAreas: ["character_proportions", "writing_speed"],
            optimalDifficulty: 2.5,
            recommendedPace: .medium
        )
    }
    
    private func generateUserLearningProfile(
        userId: String,
        analysis: UserPerformanceAnalysis
    ) async -> UserLearningProfile {
        
        // Generate comprehensive learning profile
        let learningStyle = determineLearningStyle(from: analysis)
        let cognitiveLoad = assessCognitiveLoad(from: analysis)
        let motivationFactors = identifyMotivationFactors(from: analysis)
        let performancePatterns = extractPerformancePatterns(from: analysis)
        let preferences = deriveLearningPreferences(from: analysis)
        
        return UserLearningProfile(
            userId: userId,
            learningStyle: learningStyle,
            cognitiveLoad: cognitiveLoad,
            motivationFactors: motivationFactors,
            performancePatterns: performancePatterns,
            preferences: preferences,
            strengths: analysis.strongAreas,
            challenges: analysis.weakAreas,
            lastUpdated: Date()
        )
    }
    
    // MARK: - Helper Methods
    
    private func loadAdaptiveLearningModel() {
        // Load Core ML model for adaptive learning
        // This would load a trained model for learning path optimization
    }
    
    private func setupLearningEngine() {
        // Initialize the adaptive learning engine
    }
    
    // Mock implementations for development
    private func determineOptimalPathType(profile: UserLearningProfile, analysis: UserPerformanceAnalysis) -> LearningPath.PathType {
        if analysis.learningVelocity > 3.0 && analysis.averageAccuracy > 0.85 {
            return .accelerated
        } else if analysis.averageAccuracy < 0.6 {
            return .remedial
        } else if analysis.consistencyScore < 0.7 {
            return .reinforcement
        } else {
            return .standard
        }
    }
    
    private func generateAdaptiveSteps(pathType: LearningPath.PathType, profile: UserLearningProfile, analysis: UserPerformanceAnalysis) async -> [LearningPath.AdaptiveStep] {
        // Generate adaptive learning steps based on path type and user profile
        return []
    }
    
    private func generatePersonalizedGoals(profile: UserLearningProfile, analysis: UserPerformanceAnalysis) -> [LearningPath.PersonalizedGoal] {
        return []
    }
    
    private func generateLearningMilestones(pathType: LearningPath.PathType, steps: [LearningPath.AdaptiveStep], goals: [LearningPath.PersonalizedGoal]) -> [LearningPath.Milestone] {
        return []
    }
    
    private func calculateEstimatedDuration(pathType: LearningPath.PathType, steps: [LearningPath.AdaptiveStep]) -> Int {
        return 30 // days
    }
    
    // Additional helper methods would be implemented here...
}

// MARK: - Supporting Data Models

struct UserPerformanceAnalysis {
    let userId: String
    let currentLevel: CEFRLevel
    let targetLevel: CEFRLevel
    let averageAccuracy: Double
    let learningVelocity: Double
    let consistencyScore: Double
    let retentionRate: Double
    let strongAreas: [String]
    let weakAreas: [String]
    let optimalDifficulty: Double
    let recommendedPace: LearningPace
    
    enum LearningPace {
        case slow, medium, fast, adaptive
    }
}

struct LearningPerformanceMetrics {
    let accuracy: Double
    let speed: Double
    let consistency: Double
    let errorRate: Double
    let completionTime: TimeInterval
    let attentionLevel: Double
}

struct SessionData {
    let sessionId: String
    let startTime: Date
    let duration: TimeInterval
    let charactersAttempted: [String]
    let exercisesCompleted: Int
    let userEngagement: Double
    let difficultyLevel: Double
}

// MARK: - Adaptive Learning Implementation Extensions

extension AdaptiveLearningEngine {

    // MARK: - Real-time Adaptation Methods

    func analyzePerformanceGap(
        current: LearningPerformanceMetrics,
        expected: UserLearningProfile.PerformancePatterns,
        session: SessionData
    ) -> PerformanceGap {

        let accuracyGap = current.accuracy - 0.8 // Expected accuracy threshold
        let speedGap = current.speed - expected.learningVelocity
        let consistencyGap = current.consistency - 0.75 // Expected consistency

        return PerformanceGap(
            accuracyGap: accuracyGap,
            speedGap: speedGap,
            consistencyGap: consistencyGap,
            overallGap: (accuracyGap + speedGap + consistencyGap) / 3.0,
            severity: calculateGapSeverity(accuracyGap: accuracyGap, speedGap: speedGap, consistencyGap: consistencyGap)
        )
    }

    func generateDifficultyAdjustments(
        performanceGap: PerformanceGap,
        profile: UserLearningProfile
    ) -> [AdaptiveRecommendation] {

        var recommendations: [AdaptiveRecommendation] = []

        if performanceGap.accuracyGap < -0.2 {
            // Performance is significantly below expected - reduce difficulty
            let recommendation = AdaptiveRecommendation(
                recommendationType: .difficultyChange,
                title: "Reduce Difficulty Level",
                description: "Your current accuracy suggests the content may be too challenging. Let's adjust to build confidence.",
                reasoning: "Accuracy is \(Int(abs(performanceGap.accuracyGap) * 100))% below optimal range",
                confidence: 0.85,
                impact: .high,
                actionItems: [
                    AdaptiveRecommendation.ActionItem(
                        action: "Switch to guided practice mode",
                        estimatedTime: 5,
                        difficulty: "Easy",
                        expectedOutcome: "Improved accuracy and confidence"
                    ),
                    AdaptiveRecommendation.ActionItem(
                        action: "Focus on basic character formation",
                        estimatedTime: 10,
                        difficulty: "Easy",
                        expectedOutcome: "Stronger foundation skills"
                    )
                ],
                validUntil: Calendar.current.date(byAdding: .hour, value: 2, to: Date()) ?? Date(),
                priority: .high
            )
            recommendations.append(recommendation)

        } else if performanceGap.accuracyGap > 0.15 {
            // Performance is significantly above expected - increase difficulty
            let recommendation = AdaptiveRecommendation(
                recommendationType: .difficultyChange,
                title: "Increase Challenge Level",
                description: "You're excelling! Let's introduce more challenging content to accelerate your progress.",
                reasoning: "Accuracy is \(Int(performanceGap.accuracyGap * 100))% above optimal range",
                confidence: 0.78,
                impact: .medium,
                actionItems: [
                    AdaptiveRecommendation.ActionItem(
                        action: "Try advanced character combinations",
                        estimatedTime: 15,
                        difficulty: "Advanced",
                        expectedOutcome: "Continued growth and skill development"
                    ),
                    AdaptiveRecommendation.ActionItem(
                        action: "Practice without guidance",
                        estimatedTime: 10,
                        difficulty: "Intermediate",
                        expectedOutcome: "Increased independence and confidence"
                    )
                ],
                validUntil: Calendar.current.date(byAdding: .hour, value: 3, to: Date()) ?? Date(),
                priority: .medium
            )
            recommendations.append(recommendation)
        }

        return recommendations
    }

    func generateContentRecommendations(
        performanceGap: PerformanceGap,
        profile: UserLearningProfile,
        session: SessionData
    ) -> [AdaptiveRecommendation] {

        var recommendations: [AdaptiveRecommendation] = []

        // Analyze weak areas and suggest targeted content
        for challenge in profile.challenges {
            let recommendation = AdaptiveRecommendation(
                recommendationType: .focusArea,
                title: "Focus on \(challenge.replacingOccurrences(of: "_", with: " ").capitalized)",
                description: "Targeted practice in this area will significantly improve your overall writing skills.",
                reasoning: "Analysis shows this is a key improvement area for your learning profile",
                confidence: 0.72,
                impact: .medium,
                actionItems: [
                    AdaptiveRecommendation.ActionItem(
                        action: "Complete specialized \(challenge) exercises",
                        estimatedTime: 20,
                        difficulty: "Intermediate",
                        expectedOutcome: "Improved skills in \(challenge)"
                    )
                ],
                validUntil: Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date(),
                priority: .medium
            )
            recommendations.append(recommendation)
        }

        return recommendations
    }

    func generatePacingRecommendations(
        performanceGap: PerformanceGap,
        profile: UserLearningProfile
    ) -> [AdaptiveRecommendation] {

        var recommendations: [AdaptiveRecommendation] = []

        if performanceGap.speedGap < -1.0 {
            // User is learning slower than expected
            let recommendation = AdaptiveRecommendation(
                recommendationType: .paceModification,
                title: "Adjust Learning Pace",
                description: "Let's slow down and ensure solid understanding before moving forward.",
                reasoning: "Learning velocity is below optimal range for sustained progress",
                confidence: 0.80,
                impact: .medium,
                actionItems: [
                    AdaptiveRecommendation.ActionItem(
                        action: "Extend practice time per character",
                        estimatedTime: 5,
                        difficulty: "Easy",
                        expectedOutcome: "Better retention and understanding"
                    ),
                    AdaptiveRecommendation.ActionItem(
                        action: "Add review sessions",
                        estimatedTime: 10,
                        difficulty: "Easy",
                        expectedOutcome: "Reinforced learning"
                    )
                ],
                validUntil: Calendar.current.date(byAdding: .hour, value: 4, to: Date()) ?? Date(),
                priority: .medium
            )
            recommendations.append(recommendation)
        }

        return recommendations
    }

    // MARK: - Content Generation Methods

    func calculateAdaptiveParameters(from recommendations: [AdaptiveRecommendation]) -> AdaptiveParameters {
        var difficultyMultiplier = 1.0
        var supportLevel = 0.5
        var paceAdjustment = 1.0
        var repetitionCount = 3
        var feedbackFrequency = 0.5
        var hintAvailability = true
        let timeLimit: Int? = nil
        let adaptiveScoring = true

        for recommendation in recommendations {
            switch recommendation.recommendationType {
            case .difficultyChange:
                if recommendation.title.contains("Reduce") {
                    difficultyMultiplier *= 0.8
                    supportLevel += 0.2
                    hintAvailability = true
                } else if recommendation.title.contains("Increase") {
                    difficultyMultiplier *= 1.2
                    supportLevel -= 0.1
                    hintAvailability = false
                }

            case .paceModification:
                if recommendation.title.contains("Adjust") {
                    paceAdjustment *= 0.8
                    repetitionCount += 2
                    feedbackFrequency += 0.2
                }

            case .focusArea:
                repetitionCount += 1
                feedbackFrequency += 0.1

            default:
                break
            }
        }

        return AdaptiveParameters(
            difficultyMultiplier: max(0.5, min(2.0, difficultyMultiplier)),
            supportLevel: max(0.0, min(1.0, supportLevel)),
            paceAdjustment: max(0.5, min(2.0, paceAdjustment)),
            repetitionCount: max(1, min(10, repetitionCount)),
            feedbackFrequency: max(0.1, min(1.0, feedbackFrequency)),
            hintAvailability: hintAvailability,
            timeLimit: timeLimit,
            adaptiveScoring: adaptiveScoring
        )
    }

    func determineContentType(from recommendations: [AdaptiveRecommendation]) -> PersonalizedContent.ContentType {
        let hasAccuracyFocus = recommendations.contains { $0.title.contains("accuracy") || $0.title.contains("Reduce Difficulty") }
        let hasSpeedFocus = recommendations.contains { $0.title.contains("speed") || $0.title.contains("pace") }
        let hasChallengeIncrease = recommendations.contains { $0.title.contains("Increase Challenge") }
        let hasFocusArea = recommendations.contains { $0.recommendationType == .focusArea }

        if hasAccuracyFocus {
            return .accuracyFocus
        } else if hasSpeedFocus {
            return .speedTraining
        } else if hasChallengeIncrease {
            return .challengeMode
        } else if hasFocusArea {
            return .reinforcementPractice
        } else {
            return .customExercise
        }
    }

    func generateContentTitle(from recommendations: [AdaptiveRecommendation]) -> String {
        if let primaryRecommendation = recommendations.first {
            return "Adaptive Practice: \(primaryRecommendation.title)"
        }
        return "Personalized Tamil Writing Practice"
    }

    func generateContentDescription(from recommendations: [AdaptiveRecommendation]) -> String {
        let count = recommendations.count
        if count == 1 {
            return recommendations.first?.description ?? "Customized practice session based on your performance."
        } else {
            return "Comprehensive practice session with \(count) adaptive adjustments based on your learning patterns."
        }
    }

    func extractTargetCharacters(from recommendations: [AdaptiveRecommendation]) -> [String] {
        // Extract characters that need focus based on recommendations
        var characters: [String] = []

        for recommendation in recommendations {
            if recommendation.recommendationType == .focusArea {
                // Add characters related to the focus area
                characters.append(contentsOf: getCharactersForFocusArea(recommendation.title))
            }
        }

        return characters.isEmpty ? ["அ", "ஆ", "இ"] : Array(Set(characters)) // Remove duplicates
    }

    // MARK: - Helper Methods

    private func calculateGapSeverity(accuracyGap: Double, speedGap: Double, consistencyGap: Double) -> PerformanceGap.Severity {
        let totalGap = abs(accuracyGap) + abs(speedGap) + abs(consistencyGap)

        if totalGap > 1.0 {
            return .critical
        } else if totalGap > 0.5 {
            return .high
        } else if totalGap > 0.2 {
            return .medium
        } else {
            return .low
        }
    }

    private func determineLearningStyle(from analysis: UserPerformanceAnalysis) -> UserLearningProfile.LearningStyle {
        // Analyze performance patterns to determine learning style
        return .multimodal // Default for now
    }

    private func assessCognitiveLoad(from analysis: UserPerformanceAnalysis) -> UserLearningProfile.CognitiveLoad {
        if analysis.consistencyScore < 0.6 {
            return .high
        } else if analysis.consistencyScore > 0.8 {
            return .low
        } else {
            return .medium
        }
    }

    private func identifyMotivationFactors(from analysis: UserPerformanceAnalysis) -> [UserLearningProfile.MotivationFactor] {
        var factors: [UserLearningProfile.MotivationFactor] = [.progress, .mastery]

        if analysis.learningVelocity > 2.5 {
            factors.append(.achievement)
        }

        return factors
    }

    private func extractPerformancePatterns(from analysis: UserPerformanceAnalysis) -> UserLearningProfile.PerformancePatterns {
        return UserLearningProfile.PerformancePatterns(
            optimalSessionLength: 20, // minutes
            bestPerformanceTime: "Evening",
            learningVelocity: analysis.learningVelocity,
            retentionRate: analysis.retentionRate,
            errorPatterns: analysis.weakAreas,
            improvementAreas: analysis.weakAreas
        )
    }

    private func deriveLearningPreferences(from analysis: UserPerformanceAnalysis) -> UserLearningProfile.LearningPreferences {
        return UserLearningProfile.LearningPreferences(
            preferredDifficulty: analysis.optimalDifficulty,
            feedbackFrequency: "Medium",
            supportLevel: analysis.averageAccuracy < 0.7 ? "High" : "Medium",
            practiceMode: "Adaptive",
            goalOrientation: "Mastery"
        )
    }

    private func getCharactersForFocusArea(_ focusArea: String) -> [String] {
        // Return characters related to specific focus areas
        if focusArea.contains("proportions") {
            return ["க", "ங", "ச", "ஞ"] // Characters that commonly have proportion issues
        } else if focusArea.contains("speed") {
            return ["அ", "இ", "உ"] // Simple characters for speed practice
        } else {
            return ["த", "ந", "ப", "ம"] // Default practice characters
        }
    }
}

// MARK: - Performance Gap Model

struct PerformanceGap {
    let accuracyGap: Double
    let speedGap: Double
    let consistencyGap: Double
    let overallGap: Double
    let severity: Severity

    enum Severity {
        case low, medium, high, critical

        var color: Color {
            switch self {
            case .low: return .green
            case .medium: return .yellow
            case .high: return .orange
            case .critical: return .red
            }
        }
    }
}
