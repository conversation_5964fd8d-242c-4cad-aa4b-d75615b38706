//
//  TamilWritingSystemTests.swift
//  NIRA-Tamil
//
//  Created by NIRA Team on 05/07/2025.
//

import XCTest
@testable import NIRA_Tamil

@MainActor
class TamilWritingSystemTests: XCTestCase {
    
    var scriptService: TamilScriptService!
    
    override func setUp() async throws {
        try await super.setUp()
        scriptService = TamilScriptService()
    }
    
    override func tearDown() async throws {
        scriptService = nil
        try await super.tearDown()
    }
    
    func testDatabaseConnection() async throws {
        // Test that we can load characters from the database
        await scriptService.loadAllCharacters()
        
        // Verify characters were loaded
        XCTAssertFalse(scriptService.allCharacters.isEmpty, "Characters should be loaded from database")
        XCTAssertNil(scriptService.errorMessage, "No error should occur when loading characters")
    }
    
    func testTamilCharacterTypes() async throws {
        await scriptService.loadAllCharacters()
        
        let vowels = scriptService.allCharacters.filter { $0.characterType == .vowel }
        let consonants = scriptService.allCharacters.filter { $0.characterType == .consonant }
        let combined = scriptService.allCharacters.filter { $0.characterType == .combined }
        
        // Verify we have the expected character types
        XCTAssertGreaterThan(vowels.count, 0, "Should have vowel characters")
        XCTAssertGreaterThan(consonants.count, 0, "Should have consonant characters")
        XCTAssertGreaterThan(combined.count, 0, "Should have combined characters")
        
        print("✅ Loaded \(vowels.count) vowels, \(consonants.count) consonants, \(combined.count) combinations")
    }
    
    func testTNProgressionOrder() async throws {
        await scriptService.loadAllCharacters()
        
        // Test that characters follow TN curriculum progression
        let sortedCharacters = scriptService.allCharacters
            .filter { $0.learningOrder != nil }
            .sorted { ($0.learningOrder ?? 999) < ($1.learningOrder ?? 999) }
        
        XCTAssertGreaterThan(sortedCharacters.count, 0, "Should have characters with learning order")
        
        // Verify first characters are foundation ones (ப், ம், அ)
        let firstThree = Array(sortedCharacters.prefix(3))
        let foundationChars = ["ப்", "ம்", "அ"]
        
        for (index, char) in firstThree.enumerated() {
            XCTAssertTrue(foundationChars.contains(char.character), 
                         "Character \(char.character) at position \(index) should be a foundation character")
        }
        
        print("✅ TN curriculum progression verified")
    }
    
    func testVowelLearningLevels() async throws {
        await scriptService.loadAllCharacters()
        
        let vowels = scriptService.allCharacters.filter { $0.characterType == .vowel }
        
        // Group vowels by learning progression (same logic as TamilVowelsLearningView)
        let vowelsByLevel = Dictionary(grouping: vowels) { character in
            switch character.learningOrder ?? 999 {
            case 3, 6: return 1      // அ, ஆ
            case 17, 18: return 2    // இ, ஈ
            case 19, 20: return 3    // உ, ஊ
            case 21, 22: return 4    // எ, ஏ
            case 23, 24, 25, 26: return 5  // ஐ, ஒ, ஓ, ஔ
            default: return 6
            }
        }
        
        // Verify we have vowels in each level
        XCTAssertGreaterThan(vowelsByLevel[1]?.count ?? 0, 0, "Level 1 should have vowels")
        XCTAssertGreaterThan(vowelsByLevel[2]?.count ?? 0, 0, "Level 2 should have vowels")
        
        print("✅ Vowel learning levels verified")
        for (level, vowels) in vowelsByLevel.sorted(by: { $0.key < $1.key }) {
            print("   Level \(level): \(vowels.map { $0.character }.joined(separator: ", "))")
        }
    }
    
    func testWritingContentAvailable() async throws {
        await scriptService.loadWritingContent()
        
        XCTAssertFalse(scriptService.writingContent.isEmpty, "Writing content should be available")
        
        // Check for A1 level content
        let a1Content = scriptService.writingContent.filter { $0.cefrLevel == .a1 }
        XCTAssertGreaterThan(a1Content.count, 0, "Should have A1 level writing content")
        
        print("✅ Writing content verified: \(scriptService.writingContent.count) exercises")
    }
    
    func testCharacterStrokeOrders() async throws {
        await scriptService.loadAllCharacters()
        
        // Find characters with stroke orders
        let charactersWithStrokes = scriptService.allCharacters.filter { !$0.strokeOrders.isEmpty }
        
        if !charactersWithStrokes.isEmpty {
            let firstChar = charactersWithStrokes.first!
            XCTAssertGreaterThan(firstChar.strokeOrders.count, 0, "Character should have stroke orders")
            
            // Verify stroke orders are properly sequenced
            let sortedStrokes = firstChar.strokeOrders.sorted { $0.strokeNumber < $1.strokeNumber }
            XCTAssertEqual(sortedStrokes.first?.strokeNumber, 1, "First stroke should be numbered 1")
            
            print("✅ Stroke orders verified for character: \(firstChar.character)")
        } else {
            print("⚠️ No characters with stroke orders found")
        }
    }
}

// MARK: - Performance Tests

extension TamilWritingSystemTests {
    
    func testCharacterLoadingPerformance() async throws {
        measure {
            Task {
                await scriptService.loadAllCharacters()
            }
        }
    }
    
    func testCharacterFilteringPerformance() async throws {
        await scriptService.loadAllCharacters()
        
        measure {
            let _ = scriptService.allCharacters.filter { $0.characterType == .vowel }
            let _ = scriptService.allCharacters.filter { $0.difficultyLevel <= 2 }
            let _ = scriptService.allCharacters.sorted { ($0.learningOrder ?? 999) < ($1.learningOrder ?? 999) }
        }
    }
}

// MARK: - Integration Tests

extension TamilWritingSystemTests {
    
    func testVowelsLearningViewIntegration() async throws {
        await scriptService.loadAllCharacters()
        
        // Simulate what TamilVowelsLearningView does
        let vowels = scriptService.allCharacters.filter { $0.characterType == .vowel }
        let vowelsByLevel = Dictionary(grouping: vowels) { character in
            switch character.learningOrder ?? 999 {
            case 3, 6: return 1
            case 17, 18: return 2
            case 19, 20: return 3
            case 21, 22: return 4
            case 23, 24, 25, 26: return 5
            default: return 6
            }
        }
        
        // Verify each level has appropriate content
        for level in 1...5 {
            let levelVowels = vowelsByLevel[level] ?? []
            if !levelVowels.isEmpty {
                XCTAssertGreaterThan(levelVowels.count, 0, "Level \(level) should have vowels")
                
                // Verify vowels are properly sorted by learning order
                let sortedVowels = levelVowels.sorted { 
                    ($0.learningOrder ?? 999) < ($1.learningOrder ?? 999) 
                }
                XCTAssertEqual(sortedVowels.count, levelVowels.count, "Vowels should be sortable by learning order")
            }
        }
        
        print("✅ TamilVowelsLearningView integration verified")
    }
}
